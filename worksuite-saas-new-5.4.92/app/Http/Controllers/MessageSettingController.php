<?php

namespace App\Http\Controllers;

use App\Helper\Reply;
use Illuminate\Http\Request;

class MessageSettingController extends AccountBaseController
{

    public function __construct()
    {
        parent::__construct();
        $this->pageTitle = 'app.menu.messageSettings';
        $this->activeSettingMenu = 'message_settings';

        $this->middleware(function ($request, $next) {
            abort_403(!(user()->permission('manage_message_setting') == 'all' && in_array('messages', user_modules())));

            return $next($request);
        });
    }

    public function index()
    {
        $this->messageSettings = message_setting();
        return view('message-settings.index', $this->data);
    }

    public function update(Request $request)
    {

        $setting = message_setting();

        if ($request->allow_client_admin) {
            $setting->allow_client_admin = 'yes';
        }
        else {
            $setting->allow_client_admin = 'no';
        }

        if ($request->allow_client_employee) {
            $setting->allow_client_employee = 'yes';
        }
        else {
            $setting->allow_client_employee = 'no';
        }

        $setting->restrict_client = $request->restrict_client;
        $setting->send_sound_notification = $request->send_sound_notification;
        $setting->save();

        session()->forget('message_setting');
        return Reply::success(__('messages.updateSuccess'));
    }

}
