<?php

namespace App\Http\Requests\SuperAdmin\FooterSetting;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules['title'] = 'required';
        $rules['slug'] = 'required|alpha_dash|unique:footer_menu,slug';

        if($this->get('content') == 'desc'){
            $rules['description'] = 'required';
        }
        else{
            $rules['external_link'] = 'required|url';
        }

        return $rules;
    }

}
