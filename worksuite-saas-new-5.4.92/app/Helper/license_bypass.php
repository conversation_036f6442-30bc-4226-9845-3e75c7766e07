<?php

// License bypass functions for WorkSuite SAAS
if (!function_exists('checkCompanyPackageIsValid')) {
    function checkCompanyPackageIsValid($companyId) {
        return true;
    }
}

if (!function_exists('checkCompanyCanAddMoreEmployees')) {
    function checkCompanyCanAddMoreEmployees($companyId) {
        return true;
    }
}

if (!function_exists('clearCompanyValidPackageCache')) {
    function clearCompanyValidPackageCache($companyId) {
        return true;
    }
}
