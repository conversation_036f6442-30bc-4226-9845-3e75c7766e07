---
- name: Fix Domain Configuration and Asset Paths
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"
    domain_name: "erp.iti.id.vn"

  tasks:
    - name: Check current Nginx configuration
      shell: |
        cat /etc/nginx/sites-available/worksuite
      register: nginx_config_current

    - name: Display current Nginx config
      debug:
        msg: |
          Current Nginx Configuration:
          {{ nginx_config_current.stdout }}

    - name: Check if domain is accessible from server
      shell: |
        nslookup {{ domain_name }} || echo "DNS not configured"
        ping -c 1 {{ domain_name }} || echo "Domain not reachable"
      register: domain_check
      ignore_errors: yes

    - name: Display domain check results
      debug:
        msg: |
          Domain Check Results:
          {{ domain_check.stdout }}

    - name: Check application URL configuration in .env
      shell: |
        grep -E "(APP_URL|ASSET_URL)" {{ app_root }}/.env || echo "No URL config found"
      register: env_url_check

    - name: Display current URL configuration
      debug:
        msg: |
          Current URL Configuration in .env:
          {{ env_url_check.stdout }}

    - name: Update APP_URL in .env file to use correct domain
      lineinfile:
        path: "{{ app_root }}/.env"
        regexp: '^APP_URL='
        line: 'APP_URL=https://{{ domain_name }}'
        state: present
      register: update_app_url

    - name: Add ASSET_URL to .env file
      lineinfile:
        path: "{{ app_root }}/.env"
        regexp: '^ASSET_URL='
        line: 'ASSET_URL=https://{{ domain_name }}'
        state: present
      register: add_asset_url

    - name: Check if public assets exist
      shell: |
        ls -la {{ app_root }}/public/vendor/bootstrap/css/ | head -5 || echo "Bootstrap CSS not found"
        ls -la {{ app_root }}/public/saas/ || echo "SAAS directory not found"
      register: assets_check

    - name: Display assets check
      debug:
        msg: |
          Assets Check:
          {{ assets_check.stdout }}

    - name: Create symbolic link for saas assets if needed
      shell: |
        cd {{ app_root }}/public
        if [ ! -d "saas" ]; then
          ln -sf . saas
          echo "Created saas symlink"
        else
          echo "SAAS directory already exists"
        fi
      register: create_saas_link

    - name: Display saas link creation result
      debug:
        msg: |
          SAAS Link Creation:
          {{ create_saas_link.stdout }}

    - name: Update Nginx configuration to handle domain properly
      blockinfile:
        path: /etc/nginx/sites-available/worksuite
        marker: "    # {mark} ANSIBLE MANAGED BLOCK - Domain handling"
        insertafter: "server_name"
        block: |
          
              # Handle both domain and IP access
              if ($host = "{{ domain_name }}") {
                  set $proper_host "{{ domain_name }}";
              }
              
              # Redirect /saas/ paths to root
              location ~ ^/saas/(.*)$ {
                  return 301 https://$host/$1;
              }
      register: update_nginx_config

    - name: Test Nginx configuration
      shell: |
        nginx -t
      register: nginx_test
      ignore_errors: yes

    - name: Display Nginx test results
      debug:
        msg: |
          Nginx Configuration Test:
          {{ nginx_test.stdout }}
          {{ nginx_test.stderr }}

    - name: Reload Nginx if configuration is valid
      systemd:
        name: nginx
        state: reloaded
      when: nginx_test.rc == 0

    - name: Clear Laravel caches after URL changes
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear
        sudo -u {{ app_user }} php artisan config:clear
        sudo -u {{ app_user }} php artisan route:clear
        sudo -u {{ app_user }} php artisan view:clear
      register: cache_clear_after_url

    - name: Rebuild Laravel caches with new configuration
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan config:cache
        sudo -u {{ app_user }} php artisan route:cache
        sudo -u {{ app_user }} php artisan view:cache
      register: rebuild_cache_after_url
      ignore_errors: yes

    - name: Test asset accessibility from server
      shell: |
        curl -I https://localhost:8643/vendor/bootstrap/css/bootstrap.min.css || echo "Asset not accessible via HTTPS"
        curl -I http://localhost:8601/vendor/bootstrap/css/bootstrap.min.css || echo "Asset not accessible via HTTP"
      register: asset_test_local

    - name: Display local asset test results
      debug:
        msg: |
          Local Asset Test Results:
          {{ asset_test_local.stdout }}

    - name: Check if we need to publish vendor assets
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan vendor:publish --all --force || echo "Vendor publish failed"
      register: vendor_publish
      ignore_errors: yes

    - name: Display vendor publish results
      debug:
        msg: |
          Vendor Publish Results:
          {{ vendor_publish.stdout }}

    - name: Create hosts file entry for local testing
      lineinfile:
        path: /etc/hosts
        regexp: '.*{{ domain_name }}.*'
        line: '127.0.0.1 {{ domain_name }}'
        state: present
      register: update_hosts_file

    - name: Test domain access from server after hosts update
      shell: |
        curl -I -k https://{{ domain_name }}:8643 || echo "Domain HTTPS not accessible"
        curl -I http://{{ domain_name }}:8601 || echo "Domain HTTP not accessible"
      register: domain_test_after_hosts

    - name: Display domain test results
      debug:
        msg: |
          Domain Test Results After Hosts Update:
          {{ domain_test_after_hosts.stdout }}

    - name: Display comprehensive fix results
      debug:
        msg: |
          🔧 DOMAIN AND ASSETS FIX COMPLETE
          =================================
          
          📊 Fix Results:
          - APP_URL Update: {{ 'SUCCESS' if update_app_url.changed else 'NO CHANGE' }}
          - ASSET_URL Added: {{ 'SUCCESS' if add_asset_url.changed else 'NO CHANGE' }}
          - SAAS Link: {{ 'CREATED' if 'Created' in create_saas_link.stdout else 'EXISTS' }}
          - Nginx Config: {{ 'UPDATED' if update_nginx_config.changed else 'NO CHANGE' }}
          - Nginx Test: {{ 'PASSED' if nginx_test.rc == 0 else 'FAILED' }}
          - Hosts File: {{ 'UPDATED' if update_hosts_file.changed else 'NO CHANGE' }}
          
          🌐 Current Configuration:
          - Domain: {{ domain_name }}
          - APP_URL: https://{{ domain_name }}
          - ASSET_URL: https://{{ domain_name }}
          
          🎯 Access Information:
          - Direct IP HTTPS: https://{{ ansible_host }}:8643
          - Direct IP HTTP: http://{{ ansible_host }}:8601
          - Domain (needs DNS): https://{{ domain_name }}
          
          📝 DNS Configuration Needed:
          To make {{ domain_name }} work, you need to configure DNS:
          - A Record: {{ domain_name }} → {{ ansible_host }}
          - Or update your local hosts file:
            {{ ansible_host }} {{ domain_name }}
          
          🔍 Asset Path Issues:
          - Original path: /saas/vendor/bootstrap/css/bootstrap.min.css
          - Correct path: /vendor/bootstrap/css/bootstrap.min.css
          - Nginx now redirects /saas/ paths to root
          
          ✅ Next Steps:
          1. Configure DNS for {{ domain_name }}
          2. Test asset loading after DNS is configured
          3. Clear browser cache to reload assets
