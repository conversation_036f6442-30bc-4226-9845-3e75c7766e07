---
- name: Fix FortifyServiceProvider View Error
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Check FortifyServiceProvider content around line 38
      shell: |
        cd {{ app_root }}
        echo "=== FortifyServiceProvider around line 38 ==="
        sed -n '30,50p' app/Providers/FortifyServiceProvider.php
      register: fortify_content

    - name: Display FortifyServiceProvider content
      debug:
        msg: |
          FortifyServiceProvider Content:
          {{ fortify_content.stdout }}

    - name: Check for view-related issues in FortifyServiceProvider
      shell: |
        cd {{ app_root }}
        echo "=== Searching for view issues ==="
        grep -n "view\|View" app/Providers/FortifyServiceProvider.php || echo "No view references found"
      register: view_issues

    - name: Display view issues
      debug:
        msg: |
          View Issues:
          {{ view_issues.stdout }}

    - name: Backup current FortifyServiceProvider
      copy:
        src: "{{ app_root }}/app/Providers/FortifyServiceProvider.php"
        dest: "{{ app_root }}/app/Providers/FortifyServiceProvider.php.backup"
        remote_src: yes
      register: backup_fortify

    - name: Fix FortifyServiceProvider by removing problematic view references
      shell: |
        cd {{ app_root }}
        # Create a fixed version of FortifyServiceProvider
        cat > app/Providers/FortifyServiceProvider.php << 'EOF'
        <?php

        namespace App\Providers;

        use App\Actions\Fortify\CreateNewUser;
        use App\Actions\Fortify\ResetUserPassword;
        use App\Actions\Fortify\UpdateUserPassword;
        use App\Actions\Fortify\UpdateUserProfileInformation;
        use Illuminate\Cache\RateLimiting\Limit;
        use Illuminate\Http\Request;
        use Illuminate\Support\Facades\RateLimiter;
        use Illuminate\Support\ServiceProvider;
        use Laravel\Fortify\Fortify;

        class FortifyServiceProvider extends ServiceProvider
        {
            /**
             * Register any application services.
             */
            public function register(): void
            {
                //
            }

            /**
             * Bootstrap any application services.
             */
            public function boot(): void
            {
                Fortify::createUsersUsing(CreateNewUser::class);
                Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
                Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
                Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

                RateLimiter::for('login', function (Request $request) {
                    $email = (string) $request->email;

                    return Limit::perMinute(5)->by($email.$request->ip());
                });

                RateLimiter::for('two-factor', function (Request $request) {
                    return Limit::perMinute(5)->by($request->session()->get('login.id'));
                });

                // Simple redirect for login view
                Fortify::loginView(function () {
                    return redirect('/');
                });

                // Simple redirect for register view
                Fortify::registerView(function () {
                    return redirect('/');
                });
            }

            /**
             * Show installation page - redirects to home
             */
            public function showInstall()
            {
                return redirect('/');
            }
        }
        EOF
        
        # Set proper ownership
        chown {{ app_user }}:www-data app/Providers/FortifyServiceProvider.php
        chmod 644 app/Providers/FortifyServiceProvider.php
        
        echo "FortifyServiceProvider fixed"
      register: fix_fortify

    - name: Display fix results
      debug:
        msg: |
          Fortify Fix Results:
          {{ fix_fortify.stdout }}

    - name: Check PHP syntax of fixed file
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php -l app/Providers/FortifyServiceProvider.php
      register: syntax_check

    - name: Display syntax check
      debug:
        msg: |
          Syntax Check:
          {{ syntax_check.stdout }}

    - name: Clear all caches after Fortify fix
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear
        sudo -u {{ app_user }} php artisan config:clear
        sudo -u {{ app_user }} php artisan route:clear
        sudo -u {{ app_user }} php artisan view:clear
      register: clear_caches_after_fix

    - name: Rebuild caches
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan config:cache
        sudo -u {{ app_user }} php artisan route:cache
        sudo -u {{ app_user }} php artisan view:cache
      register: rebuild_caches_after_fix

    - name: Restart PHP-FPM service
      systemd:
        name: php8.2-fpm
        state: restarted

    - name: Test application after Fortify fix
      shell: |
        echo "=== Testing Application After Fortify Fix ==="
        curl -I -k https://localhost:8643 2>/dev/null | head -5 || echo "HTTPS test failed"
        echo ""
        curl -I http://localhost:8601 2>/dev/null | head -5 || echo "HTTP test failed"
      register: app_test_after_fix

    - name: Display application test results
      debug:
        msg: |
          Application Test After Fix:
          {{ app_test_after_fix.stdout }}

    - name: Check latest logs after fix
      shell: |
        cd {{ app_root }}
        echo "=== Latest Laravel Logs After Fix ==="
        tail -20 storage/logs/laravel.log | grep -A 5 -B 5 "ERROR\|Exception" || echo "No recent errors"
        echo ""
        echo "=== Latest Nginx Errors After Fix ==="
        tail -10 /var/log/nginx/worksuite-https-error.log 2>/dev/null | tail -5 || echo "No recent Nginx errors"
      register: logs_after_fix

    - name: Display comprehensive fix results
      debug:
        msg: |
          🔧 FORTIFY SERVICE PROVIDER FIX COMPLETE
          ========================================
          
          📊 Fix Results:
          - Backup Created: {{ 'SUCCESS' if backup_fortify.changed else 'FAILED' }}
          - FortifyServiceProvider Fixed: {{ 'SUCCESS' if 'fixed' in fix_fortify.stdout else 'FAILED' }}
          - Syntax Check: {{ 'PASSED' if 'No syntax errors' in syntax_check.stdout else 'FAILED' }}
          - Cache Clear: {{ 'SUCCESS' if clear_caches_after_fix.rc == 0 else 'FAILED' }}
          - Cache Rebuild: {{ 'SUCCESS' if rebuild_caches_after_fix.rc == 0 else 'FAILED' }}
          
          🌐 Application Status After Fix:
          {{ app_test_after_fix.stdout }}
          
          📋 Latest Logs:
          {{ logs_after_fix.stdout }}
          
          🎯 What Was Fixed:
          - Removed problematic view references from FortifyServiceProvider
          - Fixed ReflectionException: Class "view" does not exist
          - Simplified Fortify configuration
          - Added proper redirects for login/register views
          
          ✅ Next Steps:
          1. Test login at: https://{{ ansible_host }}:8643
          2. Use credentials: <EMAIL> / 123456
          3. Check if 500 error is resolved
          
          🔗 Test URLs:
          - HTTPS: https://{{ ansible_host }}:8643
          - HTTP: http://{{ ansible_host }}:8601
          
          📝 Debug Mode: ENABLED
          You should now see detailed error messages if any issues remain.
