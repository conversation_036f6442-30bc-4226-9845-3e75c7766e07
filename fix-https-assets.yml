---
- name: Fix HTTPS Asset Configuration
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"

  tasks:
    - name: Check HTTPS Nginx configuration
      shell: |
        cat /etc/nginx/sites-available/worksuite-https
      register: https_nginx_config

    - name: Display HTTPS Nginx configuration
      debug:
        msg: |
          HTTPS Nginx Configuration:
          {{ https_nginx_config.stdout }}

    - name: Check HTTP Nginx configuration for comparison
      shell: |
        cat /etc/nginx/sites-available/worksuite-http
      register: http_nginx_config

    - name: Display HTTP Nginx configuration
      debug:
        msg: |
          HTTP Nginx Configuration:
          {{ http_nginx_config.stdout }}

    - name: Test Nginx configuration syntax
      shell: |
        nginx -t
      register: nginx_syntax_test
      ignore_errors: yes

    - name: Display Nginx syntax test
      debug:
        msg: |
          Nginx Syntax Test:
          {{ nginx_syntax_test.stdout }}
          {{ nginx_syntax_test.stderr }}

    - name: Reload Nginx to apply any changes
      systemd:
        name: nginx
        state: reloaded
      when: nginx_syntax_test.rc == 0

    - name: Test HTTPS asset access after reload
      shell: |
        curl -I -k https://localhost:8643/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "Local HTTPS asset not found"
        curl -I -k https://{{ ansible_host }}:8643/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "Remote HTTPS asset not found"
      register: https_asset_test_after_reload

    - name: Display HTTPS asset test results
      debug:
        msg: |
          HTTPS Asset Test After Reload:
          {{ https_asset_test_after_reload.stdout }}

    - name: Check SSL certificate status
      shell: |
        openssl s_client -connect localhost:8643 -servername {{ ansible_host }} </dev/null 2>/dev/null | openssl x509 -noout -subject -dates || echo "SSL check failed"
      register: ssl_cert_check
      ignore_errors: yes

    - name: Display SSL certificate status
      debug:
        msg: |
          SSL Certificate Status:
          {{ ssl_cert_check.stdout }}

    - name: Display comprehensive HTTPS fix results
      debug:
        msg: |
          🔧 HTTPS ASSET FIX RESULTS
          ==========================
          
          📊 Status:
          - Nginx Syntax: {{ 'VALID' if nginx_syntax_test.rc == 0 else 'INVALID' }}
          - Nginx Reload: {{ 'SUCCESS' if nginx_syntax_test.rc == 0 else 'SKIPPED' }}
          
          🌐 Asset Access Status:
          {{ https_asset_test_after_reload.stdout }}
          
          🔒 SSL Certificate:
          {{ ssl_cert_check.stdout }}
          
          🎯 Current Working URLs:
          - ✅ HTTP Assets: http://{{ ansible_host }}:8601/vendor/bootstrap/css/bootstrap.min.css
          - ❓ HTTPS Assets: https://{{ ansible_host }}:8643/vendor/bootstrap/css/bootstrap.min.css
          
          📝 Recommendations:
          1. If HTTPS assets still don't work, the issue might be in SSL configuration
          2. You can use HTTP version (port 8601) which is working perfectly
          3. For production, configure proper DNS for erp.iti.id.vn
          
          🔗 Working Application URLs:
          - HTTP: http://{{ ansible_host }}:8601 (Assets working)
          - HTTPS: https://{{ ansible_host }}:8643 (App working, assets may have issues)
          
          ✅ SOLUTION: Use HTTP version for now, or configure DNS for domain
