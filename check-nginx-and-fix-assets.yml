---
- name: Check Nginx Configuration and Fix Asset Issues
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"
    domain_name: "erp.iti.id.vn"

  tasks:
    - name: Find actual Nginx configuration files
      shell: |
        find /etc/nginx -name "*worksuite*" -o -name "*8601*" -o -name "*8643*" | head -10
        echo "---"
        ls -la /etc/nginx/sites-available/ | grep -v default || echo "No custom sites found"
        echo "---"
        ls -la /etc/nginx/sites-enabled/ | grep -v default || echo "No custom sites enabled"
      register: nginx_files_check

    - name: Display Nginx files found
      debug:
        msg: |
          Nginx Configuration Files:
          {{ nginx_files_check.stdout }}

    - name: Check current Nginx configuration in conf.d
      shell: |
        ls -la /etc/nginx/conf.d/
        echo "---"
        cat /etc/nginx/conf.d/*.conf 2>/dev/null || echo "No conf.d files found"
      register: nginx_confd_check
      ignore_errors: yes

    - name: Display Nginx conf.d content
      debug:
        msg: |
          Nginx conf.d Configuration:
          {{ nginx_confd_check.stdout }}

    - name: Check if assets exist in public directory
      shell: |
        ls -la {{ app_root }}/public/ | head -10
        echo "---"
        find {{ app_root }}/public -name "bootstrap.min.css" | head -5 || echo "Bootstrap CSS not found"
        echo "---"
        ls -la {{ app_root }}/public/vendor/ 2>/dev/null || echo "Vendor directory not found"
      register: public_assets_check

    - name: Display public assets check
      debug:
        msg: |
          Public Assets Check:
          {{ public_assets_check.stdout }}

    - name: Check current APP_URL in .env
      shell: |
        grep -E "(APP_URL|ASSET_URL)" {{ app_root }}/.env || echo "No URL config found"
      register: current_env_urls

    - name: Display current environment URLs
      debug:
        msg: |
          Current Environment URLs:
          {{ current_env_urls.stdout }}

    - name: Update APP_URL to use correct domain
      lineinfile:
        path: "{{ app_root }}/.env"
        regexp: '^APP_URL='
        line: 'APP_URL=https://{{ domain_name }}'
        state: present
      register: update_app_url

    - name: Test direct asset access via IP
      shell: |
        curl -I https://{{ ansible_host }}:8643/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "HTTPS asset not found"
        curl -I http://{{ ansible_host }}:8601/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "HTTP asset not found"
      register: direct_asset_test

    - name: Display direct asset test results
      debug:
        msg: |
          Direct Asset Test (via IP):
          {{ direct_asset_test.stdout }}

    - name: Check if Laravel mix or Vite is used for assets
      shell: |
        ls -la {{ app_root }}/public/mix-manifest.json 2>/dev/null || echo "No mix-manifest.json"
        ls -la {{ app_root }}/public/build/ 2>/dev/null || echo "No build directory"
        ls -la {{ app_root }}/webpack.mix.js 2>/dev/null || echo "No webpack.mix.js"
        ls -la {{ app_root }}/vite.config.js 2>/dev/null || echo "No vite.config.js"
      register: asset_build_check

    - name: Display asset build system check
      debug:
        msg: |
          Asset Build System Check:
          {{ asset_build_check.stdout }}

    - name: Run Laravel asset commands if available
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan storage:link || echo "Storage link failed"
        sudo -u {{ app_user }} php artisan vendor:publish --tag=public --force || echo "Vendor publish failed"
      register: laravel_asset_commands
      ignore_errors: yes

    - name: Display Laravel asset commands results
      debug:
        msg: |
          Laravel Asset Commands Results:
          {{ laravel_asset_commands.stdout }}

    - name: Create missing vendor assets directory structure
      shell: |
        cd {{ app_root }}/public
        mkdir -p vendor/bootstrap/css
        mkdir -p vendor/bootstrap/js
        mkdir -p saas/vendor/bootstrap/css
        mkdir -p saas/vendor/bootstrap/js
        echo "Created asset directories"
      register: create_asset_dirs

    - name: Copy bootstrap files if they exist elsewhere
      shell: |
        find {{ app_root }} -name "bootstrap.min.css" -not -path "*/node_modules/*" | head -5
        echo "---"
        find {{ app_root }} -name "bootstrap.min.js" -not -path "*/node_modules/*" | head -5
      register: find_bootstrap_files

    - name: Display bootstrap files search
      debug:
        msg: |
          Bootstrap Files Found:
          {{ find_bootstrap_files.stdout }}

    - name: Download bootstrap CSS if not found
      shell: |
        cd {{ app_root }}/public/vendor/bootstrap/css
        if [ ! -f "bootstrap.min.css" ]; then
          wget -q https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css -O bootstrap.min.css
          echo "Downloaded bootstrap.min.css"
        else
          echo "bootstrap.min.css already exists"
        fi
        
        # Also create in saas directory
        cd {{ app_root }}/public/saas/vendor/bootstrap/css
        if [ ! -f "bootstrap.min.css" ]; then
          cp {{ app_root }}/public/vendor/bootstrap/css/bootstrap.min.css . 2>/dev/null || wget -q https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css -O bootstrap.min.css
          echo "Created saas bootstrap.min.css"
        else
          echo "saas bootstrap.min.css already exists"
        fi
      register: download_bootstrap
      ignore_errors: yes

    - name: Display bootstrap download results
      debug:
        msg: |
          Bootstrap Download Results:
          {{ download_bootstrap.stdout }}

    - name: Set proper permissions for assets
      shell: |
        chown -R {{ app_user }}:www-data {{ app_root }}/public/vendor/
        chown -R {{ app_user }}:www-data {{ app_root }}/public/saas/ 2>/dev/null || echo "No saas directory"
        chmod -R 755 {{ app_root }}/public/vendor/
        chmod -R 755 {{ app_root }}/public/saas/ 2>/dev/null || echo "No saas directory"
      register: set_asset_permissions

    - name: Clear Laravel caches after asset changes
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear
        sudo -u {{ app_user }} php artisan config:clear
        sudo -u {{ app_user }} php artisan view:clear
      register: clear_caches_after_assets

    - name: Test asset access after fixes
      shell: |
        curl -I https://{{ ansible_host }}:8643/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "HTTPS asset still not found"
        curl -I http://{{ ansible_host }}:8601/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "HTTP asset still not found"
        curl -I https://{{ ansible_host }}:8643/saas/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "HTTPS saas asset not found"
      register: final_asset_test

    - name: Display final asset test results
      debug:
        msg: |
          Final Asset Test Results:
          {{ final_asset_test.stdout }}

    - name: Display comprehensive asset fix results
      debug:
        msg: |
          🔧 ASSET AND DOMAIN FIX COMPLETE
          ================================
          
          📊 Fix Results:
          - APP_URL Update: {{ 'SUCCESS' if update_app_url.changed else 'NO CHANGE' }}
          - Asset Directories: CREATED
          - Bootstrap Download: {{ 'SUCCESS' if 'Downloaded' in download_bootstrap.stdout else 'EXISTED' }}
          - Permissions: SET
          - Cache Clear: SUCCESS
          
          🌐 Current Configuration:
          - Domain: {{ domain_name }}
          - APP_URL: https://{{ domain_name }}
          
          🎯 Asset Access URLs:
          - Direct Bootstrap: https://{{ ansible_host }}:8643/vendor/bootstrap/css/bootstrap.min.css
          - SAAS Bootstrap: https://{{ ansible_host }}:8643/saas/vendor/bootstrap/css/bootstrap.min.css
          
          📝 DNS Configuration Still Needed:
          To make {{ domain_name }} work, configure DNS:
          - A Record: {{ domain_name }} → {{ ansible_host }}
          
          🔍 Asset Status:
          {{ final_asset_test.stdout }}
          
          ✅ Next Steps:
          1. Configure DNS for {{ domain_name }}
          2. Test the application again
          3. Clear browser cache if needed
