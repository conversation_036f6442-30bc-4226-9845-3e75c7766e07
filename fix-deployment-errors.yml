---
- name: Fix WorkSuite Deployment Errors
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Stop Laravel queue service before fixes
      systemd:
        name: laravel-queue
        state: stopped
      ignore_errors: yes

    - name: Clear all Laravel caches
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear
        sudo -u {{ app_user }} php artisan config:clear
        sudo -u {{ app_user }} php artisan route:clear
        sudo -u {{ app_user }} php artisan view:clear
        sudo -u {{ app_user }} php artisan event:clear
      register: cache_clear_result
      ignore_errors: yes

    - name: Display cache clear results
      debug:
        msg: |
          Cache Clear Results:
          {{ cache_clear_result.stdout }}

    - name: Check if FrontendController exists
      stat:
        path: "{{ app_root }}/app/Http/Controllers/SuperAdmin/FrontendController.php"
      register: frontend_controller_check

    - name: Display controller check result
      debug:
        msg: |
          FrontendController exists: {{ frontend_controller_check.stat.exists }}
          Path: {{ app_root }}/app/Http/Controllers/SuperAdmin/FrontendController.php

    - name: Create missing showInstall method in FrontendController
      blockinfile:
        path: "{{ app_root }}/app/Http/Controllers/SuperAdmin/FrontendController.php"
        marker: "    // {mark} ANSIBLE MANAGED BLOCK - showInstall method"
        insertbefore: "^}"
        block: |
          
              /**
               * Show installation page
               */
              public function showInstall()
              {
                  return redirect()->route('login');
              }
      when: frontend_controller_check.stat.exists
      register: add_method_result
      ignore_errors: yes

    - name: Check if config/app.php exists
      stat:
        path: "{{ app_root }}/config/app.php"
      register: app_config_check

    - name: Fix View service provider in config/app.php
      replace:
        path: "{{ app_root }}/config/app.php"
        regexp: "'view',"
        replace: "Illuminate\\View\\ViewServiceProvider::class,"
      when: app_config_check.stat.exists
      register: fix_view_provider
      ignore_errors: yes

    - name: Regenerate Laravel autoload files
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} composer dump-autoload --optimize
      register: autoload_result
      ignore_errors: yes

    - name: Display autoload results
      debug:
        msg: |
          Autoload Results:
          {{ autoload_result.stdout }}
          
          Errors (if any):
          {{ autoload_result.stderr }}

    - name: Rebuild Laravel configuration cache
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan config:cache
        sudo -u {{ app_user }} php artisan route:cache
        sudo -u {{ app_user }} php artisan view:cache
      register: rebuild_cache_result
      ignore_errors: yes

    - name: Display rebuild cache results
      debug:
        msg: |
          Rebuild Cache Results:
          {{ rebuild_cache_result.stdout }}

    - name: Check Laravel application status
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan about
      register: laravel_about
      ignore_errors: yes

    - name: Display Laravel status
      debug:
        msg: |
          Laravel Application Status:
          {{ laravel_about.stdout }}

    - name: Test route list again
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan route:list | head -5
      register: routes_test_after
      ignore_errors: yes

    - name: Display routes after fix
      debug:
        msg: |
          Routes After Fix:
          {{ routes_test_after.stdout }}

    - name: Set proper file ownership and permissions
      shell: |
        chown -R {{ app_user }}:www-data {{ app_root }}
        chmod -R 755 {{ app_root }}
        chmod -R 775 {{ app_root }}/storage
        chmod -R 775 {{ app_root }}/bootstrap/cache
        chmod 644 {{ app_root }}/.env
      register: permissions_fix

    - name: Restart PHP-FPM service
      systemd:
        name: php8.2-fpm
        state: restarted

    - name: Restart Nginx service
      systemd:
        name: nginx
        state: restarted

    - name: Start Laravel queue service
      systemd:
        name: laravel-queue
        state: started
        enabled: yes

    - name: Wait for services to be ready
      wait_for:
        port: "{{ item }}"
        host: localhost
        timeout: 30
      loop:
        - 8601
        - 8643

    - name: Test application response after fixes
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 10
      register: app_test_after_fix
      ignore_errors: yes

    - name: Test HTTP response after fixes
      uri:
        url: "http://localhost:8601"
        method: GET
        timeout: 10
      register: http_test_after_fix
      ignore_errors: yes

    - name: Check latest Laravel logs after fixes
      shell: |
        tail -10 {{ app_root }}/storage/logs/laravel.log 2>/dev/null || echo "No new log entries"
      register: latest_logs

    - name: Display final fix results
      debug:
        msg: |
          🔧 DEPLOYMENT FIXES COMPLETE
          ============================
          
          📊 Fix Results:
          - Cache Clear: {{ 'SUCCESS' if cache_clear_result.rc == 0 else 'FAILED' }}
          - Controller Fix: {{ 'SUCCESS' if add_method_result.changed else 'SKIPPED' }}
          - View Provider Fix: {{ 'SUCCESS' if fix_view_provider.changed else 'SKIPPED' }}
          - Autoload: {{ 'SUCCESS' if autoload_result.rc == 0 else 'FAILED' }}
          - Cache Rebuild: {{ 'SUCCESS' if rebuild_cache_result.rc == 0 else 'FAILED' }}
          
          🌐 Application Status:
          - HTTPS Response: {{ app_test_after_fix.status | default('Failed') }}
          - HTTP Response: {{ http_test_after_fix.status | default('Failed') }}
          
          📋 Latest Logs:
          {{ latest_logs.stdout }}
          
          🎯 Next Steps:
          {% if app_test_after_fix.status == 200 or http_test_after_fix.status == 200 %}
          ✅ SUCCESS! Application is now working
          - Access: https://{{ ansible_host }}:8643
          - Access: http://{{ ansible_host }}:8601
          {% elif app_test_after_fix.status == 302 or http_test_after_fix.status == 302 %}
          ✅ REDIRECT! Application is working (redirecting to login)
          - Access: https://{{ ansible_host }}:8643
          - Access: http://{{ ansible_host }}:8601
          {% else %}
          ⚠️  Still having issues. Check the logs above for more details.
          {% endif %}
          
          🔑 If working, you should see:
          - Login page or setup wizard
          - No more 500 errors
          - Proper Laravel application response
