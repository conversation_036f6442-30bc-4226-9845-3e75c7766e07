---
- name: Comprehensive Laravel Configuration Fix for Domain and Assets
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"
    domain_name: "erp.iti.id.vn"

  tasks:
    - name: Check current Laravel configuration
      shell: |
        cd {{ app_root }}
        echo "=== Current .env configuration ==="
        grep -E "(APP_URL|ASSET_URL|MIX_|VITE_)" .env || echo "No URL configs found"
        echo ""
        echo "=== Current config/app.php URL ==="
        sudo -u {{ app_user }} php artisan config:show app.url || echo "Config show failed"
        echo ""
        echo "=== Mix manifest ==="
        cat public/mix-manifest.json 2>/dev/null || echo "No mix-manifest.json"
      register: current_laravel_config

    - name: Display current Laravel configuration
      debug:
        msg: |
          Current Laravel Configuration:
          {{ current_laravel_config.stdout }}

    - name: Check DNS resolution for domain
      shell: |
        nslookup {{ domain_name }} || echo "DNS not configured"
        ping -c 1 {{ domain_name }} || echo "Domain not reachable"
      register: dns_check
      ignore_errors: yes

    - name: Display DNS check
      debug:
        msg: |
          DNS Check Results:
          {{ dns_check.stdout }}

    - name: Update .env file with correct domain configuration
      lineinfile:
        path: "{{ app_root }}/.env"
        regexp: "{{ item.regexp }}"
        line: "{{ item.line }}"
        state: present
      loop:
        - { regexp: '^APP_URL=', line: 'APP_URL=https://{{ domain_name }}' }
        - { regexp: '^ASSET_URL=', line: 'ASSET_URL=https://{{ domain_name }}' }
        - { regexp: '^MIX_APP_URL=', line: 'MIX_APP_URL=https://{{ domain_name }}' }
      register: update_env_urls

    - name: Add missing environment variables if not present
      lineinfile:
        path: "{{ app_root }}/.env"
        line: "{{ item }}"
        state: present
      loop:
        - "ASSET_URL=https://{{ domain_name }}"
        - "MIX_APP_URL=https://{{ domain_name }}"
      register: add_missing_env_vars

    - name: Update Nginx configuration to handle domain properly
      blockinfile:
        path: /etc/nginx/sites-available/worksuite-https
        marker: "    # {mark} ANSIBLE MANAGED BLOCK - Domain and asset handling"
        insertafter: "server_name"
        block: |
          
              # Handle domain access
              if ($host != "{{ domain_name }}") {
                  return 301 https://{{ domain_name }}$request_uri;
              }
              
              # Remove /saas/ prefix from asset requests
              location ~ ^/saas/(.*)$ {
                  return 301 https://{{ domain_name }}/$1;
              }
              
              # Ensure proper asset serving
              location ~* ^/(css|js|images|fonts|vendor|build|mix-manifest\.json)/ {
                  expires 1y;
                  add_header Cache-Control "public, immutable";
                  access_log off;
                  try_files $uri $uri/ =404;
              }
      register: update_nginx_https

    - name: Update HTTP Nginx configuration similarly
      blockinfile:
        path: /etc/nginx/sites-available/worksuite-http
        marker: "    # {mark} ANSIBLE MANAGED BLOCK - Domain and asset handling"
        insertafter: "server_name"
        block: |
          
              # Handle domain access
              if ($host != "{{ domain_name }}") {
                  return 301 http://{{ domain_name }}$request_uri;
              }
              
              # Remove /saas/ prefix from asset requests
              location ~ ^/saas/(.*)$ {
                  return 301 http://{{ domain_name }}/$1;
              }
              
              # Ensure proper asset serving
              location ~* ^/(css|js|images|fonts|vendor|build|mix-manifest\.json)/ {
                  expires 1y;
                  add_header Cache-Control "public, immutable";
                  access_log off;
                  try_files $uri $uri/ =404;
              }
      register: update_nginx_http

    - name: Check if WowJS exists and create if missing
      shell: |
        cd {{ app_root }}/public
        mkdir -p vendor/wowjs
        if [ ! -f "vendor/wowjs/wow.min.js" ]; then
          wget -q https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js -O vendor/wowjs/wow.min.js
          echo "Downloaded wow.min.js"
        else
          echo "wow.min.js already exists"
        fi
        
        # Also create in saas directory for backward compatibility
        mkdir -p saas/vendor/wowjs
        if [ ! -f "saas/vendor/wowjs/wow.min.js" ]; then
          cp vendor/wowjs/wow.min.js saas/vendor/wowjs/wow.min.js 2>/dev/null || wget -q https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js -O saas/vendor/wowjs/wow.min.js
          echo "Created saas wow.min.js"
        else
          echo "saas wow.min.js already exists"
        fi
      register: create_wowjs
      ignore_errors: yes

    - name: Display WowJS creation results
      debug:
        msg: |
          WowJS Creation Results:
          {{ create_wowjs.stdout }}

    - name: Set proper permissions for all assets
      shell: |
        chown -R {{ app_user }}:www-data {{ app_root }}/public/vendor/
        chown -R {{ app_user }}:www-data {{ app_root }}/public/saas/ 2>/dev/null || echo "No saas directory"
        chmod -R 755 {{ app_root }}/public/vendor/
        chmod -R 755 {{ app_root }}/public/saas/ 2>/dev/null || echo "No saas directory"
      register: set_permissions

    - name: Test Nginx configuration
      shell: |
        nginx -t
      register: nginx_test
      ignore_errors: yes

    - name: Display Nginx test results
      debug:
        msg: |
          Nginx Test Results:
          {{ nginx_test.stdout }}
          {{ nginx_test.stderr }}

    - name: Reload Nginx if configuration is valid
      systemd:
        name: nginx
        state: reloaded
      when: nginx_test.rc == 0

    - name: Clear all Laravel caches
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear
        sudo -u {{ app_user }} php artisan config:clear
        sudo -u {{ app_user }} php artisan route:clear
        sudo -u {{ app_user }} php artisan view:clear
      register: clear_all_caches

    - name: Rebuild Laravel caches with new configuration
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan config:cache
        sudo -u {{ app_user }} php artisan route:cache
        sudo -u {{ app_user }} php artisan view:cache
      register: rebuild_caches
      ignore_errors: yes

    - name: Add domain to server hosts file for local resolution
      lineinfile:
        path: /etc/hosts
        regexp: '.*{{ domain_name }}.*'
        line: '127.0.0.1 {{ domain_name }}'
        state: present
      register: update_hosts

    - name: Test asset access after configuration
      shell: |
        echo "Testing direct asset access:"
        curl -I -k https://{{ domain_name }}/vendor/wowjs/wow.min.js 2>/dev/null || echo "Domain HTTPS wow.min.js not found"
        curl -I -k https://{{ domain_name }}/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "Domain HTTPS bootstrap not found"
        echo ""
        echo "Testing via IP:"
        curl -I -k https://{{ ansible_host }}:8643/vendor/wowjs/wow.min.js 2>/dev/null || echo "IP HTTPS wow.min.js not found"
        curl -I -k https://{{ ansible_host }}:8643/vendor/bootstrap/css/bootstrap.min.css 2>/dev/null || echo "IP HTTPS bootstrap not found"
      register: final_asset_test

    - name: Display final asset test results
      debug:
        msg: |
          Final Asset Test Results:
          {{ final_asset_test.stdout }}

    - name: Check Laravel configuration after changes
      shell: |
        cd {{ app_root }}
        echo "=== Updated Laravel config ==="
        sudo -u {{ app_user }} php artisan config:show app.url
        echo ""
        echo "=== Environment URLs ==="
        grep -E "(APP_URL|ASSET_URL|MIX_)" .env
      register: final_laravel_config

    - name: Display final Laravel configuration
      debug:
        msg: |
          Final Laravel Configuration:
          {{ final_laravel_config.stdout }}

    - name: Display comprehensive fix results
      debug:
        msg: |
          🔧 COMPREHENSIVE LARAVEL CONFIG FIX COMPLETE
          ============================================
          
          📊 Fix Results:
          - Environment URLs: {{ 'UPDATED' if update_env_urls.changed else 'NO CHANGE' }}
          - Nginx HTTPS Config: {{ 'UPDATED' if update_nginx_https.changed else 'NO CHANGE' }}
          - Nginx HTTP Config: {{ 'UPDATED' if update_nginx_http.changed else 'NO CHANGE' }}
          - WowJS Assets: {{ 'CREATED' if 'Downloaded' in create_wowjs.stdout else 'EXISTED' }}
          - Nginx Test: {{ 'PASSED' if nginx_test.rc == 0 else 'FAILED' }}
          - Hosts File: {{ 'UPDATED' if update_hosts.changed else 'NO CHANGE' }}
          
          🌐 Current Configuration:
          - Domain: {{ domain_name }}
          - APP_URL: https://{{ domain_name }}
          - ASSET_URL: https://{{ domain_name }}
          
          🎯 Asset URLs (Should work now):
          - Bootstrap: https://{{ domain_name }}/vendor/bootstrap/css/bootstrap.min.css
          - WowJS: https://{{ domain_name }}/vendor/wowjs/wow.min.js
          
          📝 DNS Status:
          {{ dns_check.stdout }}
          
          🔍 Asset Test Results:
          {{ final_asset_test.stdout }}
          
          ✅ Next Steps:
          1. Configure DNS: A Record {{ domain_name }} → {{ ansible_host }}
          2. Test application at https://{{ domain_name }}
          3. Clear browser cache to reload assets
          4. Check that /saas/ paths are redirected properly
          
          🚨 Important Notes:
          - Nginx now redirects /saas/ paths to root
          - Assets are served from /vendor/ not /saas/vendor/
          - Domain must resolve to {{ ansible_host }} for full functionality
