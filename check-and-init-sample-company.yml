---
- name: <PERSON> Login Accounts and Initialize Sample Company
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Check database connection and tables
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan migrate:status | head -20
      register: migration_status
      ignore_errors: yes

    - name: Display migration status
      debug:
        msg: |
          Migration Status:
          {{ migration_status.stdout }}

    - name: Check if users table exists and has data
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            \$userCount = \App\Models\User::count();
            echo 'Total users: ' . \$userCount . PHP_EOL;
            if (\$userCount > 0) {
                \$users = \App\Models\User::select('id', 'name', 'email', 'created_at')->limit(5)->get();
                foreach (\$users as \$user) {
                    echo 'ID: ' . \$user->id . ', Name: ' . \$user->name . ', Email: ' . \$user->email . ', Created: ' . \$user->created_at . PHP_EOL;
                }
            }
        } catch (Exception \$e) {
            echo 'Error checking users: ' . \$e->getMessage() . PHP_EOL;
        }
        "
      register: check_users
      ignore_errors: yes

    - name: Display users check
      debug:
        msg: |
          Users Check:
          {{ check_users.stdout }}

    - name: Check if companies table exists and has data
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            \$companyCount = \App\Models\Company::count();
            echo 'Total companies: ' . \$companyCount . PHP_EOL;
            if (\$companyCount > 0) {
                \$companies = \App\Models\Company::select('id', 'company_name', 'company_email', 'created_at')->limit(5)->get();
                foreach (\$companies as \$company) {
                    echo 'ID: ' . \$company->id . ', Name: ' . \$company->company_name . ', Email: ' . \$company->company_email . ', Created: ' . \$company->created_at . PHP_EOL;
                }
            }
        } catch (Exception \$e) {
            echo 'Error checking companies: ' . \$e->getMessage() . PHP_EOL;
        }
        "
      register: check_companies
      ignore_errors: yes

    - name: Display companies check
      debug:
        msg: |
          Companies Check:
          {{ check_companies.stdout }}

    - name: Check available artisan commands for seeding/initialization
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan list | grep -E "(seed|install|setup|init|demo|sample)" || echo "No seeding commands found"
      register: available_commands

    - name: Display available commands
      debug:
        msg: |
          Available Seeding/Setup Commands:
          {{ available_commands.stdout }}

    - name: Check if database seeders exist
      shell: |
        find {{ app_root }}/database/seeders -name "*.php" | head -10
        echo "---"
        ls -la {{ app_root }}/database/seeders/ | head -10
      register: check_seeders

    - name: Display seeders
      debug:
        msg: |
          Database Seeders:
          {{ check_seeders.stdout }}

    - name: Run database seeder if no users exist
      shell: |
        cd {{ app_root }}
        USER_COUNT=$(sudo -u {{ app_user }} php artisan tinker --execute="echo \App\Models\User::count();" 2>/dev/null | tail -1)
        echo "Current user count: $USER_COUNT"
        
        if [ "$USER_COUNT" = "0" ] || [ -z "$USER_COUNT" ]; then
          echo "No users found, running database seeder..."
          sudo -u {{ app_user }} php artisan db:seed --force
          echo "Database seeder completed"
        else
          echo "Users already exist, skipping seeder"
        fi
      register: run_seeder
      ignore_errors: yes

    - name: Display seeder results
      debug:
        msg: |
          Seeder Results:
          {{ run_seeder.stdout }}

    - name: Create sample admin user if no users exist
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            \$userCount = \App\Models\User::count();
            if (\$userCount == 0) {
                \$user = new \App\Models\User();
                \$user->name = 'Admin User';
                \$user->email = '<EMAIL>';
                \$user->password = bcrypt('password123');
                \$user->email_verified_at = now();
                \$user->save();
                echo 'Sample admin user created successfully' . PHP_EOL;
                echo 'Email: <EMAIL>' . PHP_EOL;
                echo 'Password: password123' . PHP_EOL;
            } else {
                echo 'Users already exist, no need to create sample user' . PHP_EOL;
            }
        } catch (Exception \$e) {
            echo 'Error creating sample user: ' . \$e->getMessage() . PHP_EOL;
        }
        "
      register: create_sample_user
      ignore_errors: yes

    - name: Display sample user creation results
      debug:
        msg: |
          Sample User Creation:
          {{ create_sample_user.stdout }}

    - name: Create sample company if no companies exist
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            \$companyCount = \App\Models\Company::count();
            if (\$companyCount == 0) {
                \$company = new \App\Models\Company();
                \$company->company_name = 'Sample Company Ltd';
                \$company->company_email = '<EMAIL>';
                \$company->company_phone = '+84-***********';
                \$company->website = 'https://samplecompany.com';
                \$company->address = '123 Sample Street, Ho Chi Minh City, Vietnam';
                \$company->timezone = 'Asia/Ho_Chi_Minh';
                \$company->currency_id = 1;
                \$company->package_id = 1;
                \$company->status = 'active';
                \$company->licence_expire_on = now()->addYear();
                \$company->save();
                echo 'Sample company created successfully' . PHP_EOL;
                echo 'Company: Sample Company Ltd' . PHP_EOL;
                echo 'Email: <EMAIL>' . PHP_EOL;
            } else {
                echo 'Companies already exist, no need to create sample company' . PHP_EOL;
            }
        } catch (Exception \$e) {
            echo 'Error creating sample company: ' . \$e->getMessage() . PHP_EOL;
        }
        "
      register: create_sample_company
      ignore_errors: yes

    - name: Display sample company creation results
      debug:
        msg: |
          Sample Company Creation:
          {{ create_sample_company.stdout }}

    - name: Check final status of users and companies
      shell: |
        cd {{ app_root }}
        echo "=== FINAL STATUS ==="
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            echo 'Users: ' . \App\Models\User::count() . PHP_EOL;
            echo 'Companies: ' . \App\Models\Company::count() . PHP_EOL;
            
            if (\App\Models\User::count() > 0) {
                echo PHP_EOL . 'Sample Users:' . PHP_EOL;
                \$users = \App\Models\User::select('id', 'name', 'email')->limit(3)->get();
                foreach (\$users as \$user) {
                    echo '- ' . \$user->name . ' (' . \$user->email . ')' . PHP_EOL;
                }
            }
            
            if (\App\Models\Company::count() > 0) {
                echo PHP_EOL . 'Sample Companies:' . PHP_EOL;
                \$companies = \App\Models\Company::select('id', 'company_name', 'company_email')->limit(3)->get();
                foreach (\$companies as \$company) {
                    echo '- ' . \$company->company_name . ' (' . \$company->company_email . ')' . PHP_EOL;
                }
            }
        } catch (Exception \$e) {
            echo 'Error: ' . \$e->getMessage() . PHP_EOL;
        }
        "
      register: final_status

    - name: Display comprehensive results
      debug:
        msg: |
          🔧 ACCOUNT CHECK AND SAMPLE DATA INITIALIZATION COMPLETE
          ========================================================
          
          📊 Database Status:
          {{ migration_status.stdout | default('Migration check failed') }}
          
          👥 Users Status:
          {{ check_users.stdout | default('User check failed') }}
          
          🏢 Companies Status:
          {{ check_companies.stdout | default('Company check failed') }}
          
          🌱 Seeder Results:
          {{ run_seeder.stdout | default('Seeder not run') }}
          
          👤 Sample User Creation:
          {{ create_sample_user.stdout | default('Sample user creation failed') }}
          
          🏢 Sample Company Creation:
          {{ create_sample_company.stdout | default('Sample company creation failed') }}
          
          📋 Final Status:
          {{ final_status.stdout | default('Final status check failed') }}
          
          🎯 Login Information:
          {% if 'Sample admin user created successfully' in create_sample_user.stdout %}
          ✅ Sample Admin Account Created:
          - Email: <EMAIL>
          - Password: password123
          - URL: https://erp.iti.id.vn (or https://{{ ansible_host }}:8643)
          {% else %}
          ℹ️  Check existing users above for login credentials
          {% endif %}
          
          🏢 Company Information:
          {% if 'Sample company created successfully' in create_sample_company.stdout %}
          ✅ Sample Company Created:
          - Name: Sample Company Ltd
          - Email: <EMAIL>
          {% else %}
          ℹ️  Check existing companies above
          {% endif %}
          
          🔗 Access URLs:
          - Domain: https://erp.iti.id.vn (needs DNS configuration)
          - Direct IP: https://{{ ansible_host }}:8643
          - HTTP: http://{{ ansible_host }}:8601
          
          📝 Next Steps:
          1. Access the application using the URLs above
          2. Login with the provided credentials
          3. Complete any remaining setup wizard steps
          4. Configure DNS for domain access
