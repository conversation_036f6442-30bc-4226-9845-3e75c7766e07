---
- name: Fix showInstall Method in Base Controllers
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Find all controllers that might need showInstall method
      shell: |
        find {{ app_root }}/app/Http/Controllers -name "*.php" -exec grep -l "showInstall" {} \; || echo "No controllers found with showInstall"
      register: controllers_with_showinstall

    - name: Display controllers with showInstall
      debug:
        msg: |
          Controllers with showInstall references:
          {{ controllers_with_showinstall.stdout }}

    - name: Check if there's a base controller for SuperAdmin
      stat:
        path: "{{ app_root }}/app/Http/Controllers/SuperAdmin/FrontBaseController.php"
      register: front_base_controller_check

    - name: Display base controller check
      debug:
        msg: |
          FrontBaseController exists: {{ front_base_controller_check.stat.exists }}

    - name: Check current FrontBaseController content
      shell: |
        head -30 {{ app_root }}/app/Http/Controllers/SuperAdmin/FrontBaseController.php
      register: front_base_content
      when: front_base_controller_check.stat.exists

    - name: Display FrontBaseController content
      debug:
        msg: |
          FrontBaseController content:
          {{ front_base_content.stdout }}
      when: front_base_controller_check.stat.exists

    - name: Add showInstall method to FrontBaseController
      blockinfile:
        path: "{{ app_root }}/app/Http/Controllers/SuperAdmin/FrontBaseController.php"
        marker: "    // {mark} ANSIBLE MANAGED BLOCK - showInstall method"
        insertbefore: "^}"
        block: |
          
              /**
               * Show installation page - redirects to home
               */
              public function showInstall()
              {
                  return redirect('/');
              }
      when: front_base_controller_check.stat.exists
      register: add_showinstall_base

    - name: Check if there are other base controllers
      shell: |
        find {{ app_root }}/app/Http/Controllers -name "*BaseController.php" -o -name "*Base.php" | head -10
      register: other_base_controllers

    - name: Display other base controllers
      debug:
        msg: |
          Other base controllers found:
          {{ other_base_controllers.stdout }}

    - name: Check main Controller base class
      stat:
        path: "{{ app_root }}/app/Http/Controllers/Controller.php"
      register: main_controller_check

    - name: Add showInstall method to main Controller if needed
      blockinfile:
        path: "{{ app_root }}/app/Http/Controllers/Controller.php"
        marker: "    // {mark} ANSIBLE MANAGED BLOCK - showInstall method"
        insertbefore: "^}"
        block: |
          
              /**
               * Show installation page - redirects to home
               */
              public function showInstall()
              {
                  return redirect('/');
              }
      when: main_controller_check.stat.exists
      register: add_showinstall_main

    - name: Check for CompanyRegisterController specifically
      stat:
        path: "{{ app_root }}/app/Http/Controllers/SuperAdmin/CompanyRegisterController.php"
      register: company_register_controller_check

    - name: Add showInstall method to CompanyRegisterController directly
      blockinfile:
        path: "{{ app_root }}/app/Http/Controllers/SuperAdmin/CompanyRegisterController.php"
        marker: "    // {mark} ANSIBLE MANAGED BLOCK - showInstall method"
        insertbefore: "^}"
        block: |
          
              /**
               * Show installation page - redirects to home
               */
              public function showInstall()
              {
                  return redirect('/');
              }
      when: company_register_controller_check.stat.exists
      register: add_showinstall_company

    - name: Find all controllers that extend FrontBaseController
      shell: |
        grep -r "extends.*FrontBaseController" {{ app_root }}/app/Http/Controllers/ | cut -d: -f1 | head -10
      register: controllers_extending_base
      ignore_errors: yes

    - name: Display controllers extending base
      debug:
        msg: |
          Controllers extending FrontBaseController:
          {{ controllers_extending_base.stdout }}

    - name: Clear Laravel caches after adding methods
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear
        sudo -u {{ app_user }} php artisan config:clear
        sudo -u {{ app_user }} php artisan route:clear
        sudo -u {{ app_user }} php artisan view:clear
      register: cache_clear_after_methods

    - name: Check for syntax errors in modified files
      shell: |
        cd {{ app_root }}
        {% if front_base_controller_check.stat.exists %}
        sudo -u {{ app_user }} php -l app/Http/Controllers/SuperAdmin/FrontBaseController.php
        {% endif %}
        {% if main_controller_check.stat.exists %}
        sudo -u {{ app_user }} php -l app/Http/Controllers/Controller.php
        {% endif %}
        {% if company_register_controller_check.stat.exists %}
        sudo -u {{ app_user }} php -l app/Http/Controllers/SuperAdmin/CompanyRegisterController.php
        {% endif %}
      register: syntax_check_all
      ignore_errors: yes

    - name: Display syntax check results
      debug:
        msg: |
          Syntax check results:
          {{ syntax_check_all.stdout }}

    - name: Restart PHP-FPM service
      systemd:
        name: php8.2-fpm
        state: restarted

    - name: Test application response after base controller fix
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 10
      register: app_test_after_base_fix
      ignore_errors: yes

    - name: Check latest Laravel logs after base controller fix
      shell: |
        tail -15 {{ app_root }}/storage/logs/laravel.log 2>/dev/null || echo "No new log entries"
      register: latest_logs_base

    - name: Display base controller fix results
      debug:
        msg: |
          🔧 BASE CONTROLLER FIX COMPLETE
          ===============================
          
          📊 Fix Results:
          - FrontBaseController Fix: {{ 'SUCCESS' if add_showinstall_base.changed else 'SKIPPED' }}
          - Main Controller Fix: {{ 'SUCCESS' if add_showinstall_main.changed else 'SKIPPED' }}
          - CompanyRegisterController Fix: {{ 'SUCCESS' if add_showinstall_company.changed else 'SKIPPED' }}
          - Cache Clear: {{ 'SUCCESS' if cache_clear_after_methods.rc == 0 else 'FAILED' }}
          - Syntax Check: {{ 'PASSED' if 'No syntax errors' in syntax_check_all.stdout else 'CHECK NEEDED' }}
          
          🌐 Application Status:
          - HTTPS Response: {{ app_test_after_base_fix.status | default('Failed') }}
          
          📋 Latest Logs:
          {{ latest_logs_base.stdout }}
          
          🎯 Status:
          {% if app_test_after_base_fix.status == 200 %}
          ✅ SUCCESS! Base controller error fixed, application working
          {% elif app_test_after_base_fix.status == 302 %}
          ✅ REDIRECT! Application working (redirecting properly)
          {% else %}
          ⚠️  Still having issues. Check the logs above.
          {% endif %}
          
          🔗 Access URLs:
          - HTTPS: https://{{ ansible_host }}:8643
          - HTTP: http://{{ ansible_host }}:8601
