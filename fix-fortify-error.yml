---
- name: Fix FortifyServiceProvider showInstall Error
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Check FortifyServiceProvider file
      stat:
        path: "{{ app_root }}/app/Providers/FortifyServiceProvider.php"
      register: fortify_provider_check

    - name: Display FortifyServiceProvider check result
      debug:
        msg: |
          FortifyServiceProvider exists: {{ fortify_provider_check.stat.exists }}
          Path: {{ app_root }}/app/Providers/FortifyServiceProvider.php

    - name: Read current FortifyServiceProvider content around line 181
      shell: |
        sed -n '175,185p' {{ app_root }}/app/Providers/FortifyServiceProvider.php
      register: fortify_content
      when: fortify_provider_check.stat.exists

    - name: Display current FortifyServiceProvider content
      debug:
        msg: |
          Current content around line 181:
          {{ fortify_content.stdout }}

    - name: Fix FortifyServiceProvider showInstall method call
      replace:
        path: "{{ app_root }}/app/Providers/FortifyServiceProvider.php"
        regexp: '\$this->showInstall\(\)'
        replace: 'redirect()->route("login")'
      when: fortify_provider_check.stat.exists
      register: fix_fortify_result

    - name: Alternative fix - replace any showInstall() calls
      replace:
        path: "{{ app_root }}/app/Providers/FortifyServiceProvider.php"
        regexp: '->showInstall\(\)'
        replace: '->redirect()->route("login")'
      when: fortify_provider_check.stat.exists and not fix_fortify_result.changed
      register: fix_fortify_alt_result

    - name: Check if we need to find the exact line with showInstall
      shell: |
        grep -n "showInstall" {{ app_root }}/app/Providers/FortifyServiceProvider.php || echo "No showInstall found"
      register: grep_showinstall
      when: fortify_provider_check.stat.exists

    - name: Display grep results
      debug:
        msg: |
          Grep results for showInstall:
          {{ grep_showinstall.stdout }}

    - name: Manual fix for line 181 if needed
      lineinfile:
        path: "{{ app_root }}/app/Providers/FortifyServiceProvider.php"
        regexp: '.*showInstall.*'
        line: '            return redirect()->route("login");'
        state: present
      when: fortify_provider_check.stat.exists and "'showInstall' in grep_showinstall.stdout"
      register: manual_fix_result

    - name: Clear Laravel caches after fix
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear
        sudo -u {{ app_user }} php artisan config:clear
        sudo -u {{ app_user }} php artisan route:clear
        sudo -u {{ app_user }} php artisan view:clear
      register: cache_clear_after_fix

    - name: Restart PHP-FPM service
      systemd:
        name: php8.2-fpm
        state: restarted

    - name: Test application response after Fortify fix
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 10
      register: app_test_after_fortify_fix
      ignore_errors: yes

    - name: Test HTTP response after Fortify fix
      uri:
        url: "http://localhost:8601"
        method: GET
        timeout: 10
      register: http_test_after_fortify_fix
      ignore_errors: yes

    - name: Check latest Laravel logs after Fortify fix
      shell: |
        tail -15 {{ app_root }}/storage/logs/laravel.log 2>/dev/null || echo "No new log entries"
      register: latest_logs_fortify

    - name: Display Fortify fix results
      debug:
        msg: |
          🔧 FORTIFY FIX COMPLETE
          =======================
          
          📊 Fix Results:
          - FortifyServiceProvider Fix: {{ 'SUCCESS' if fix_fortify_result.changed or fix_fortify_alt_result.changed or manual_fix_result.changed else 'NO CHANGES NEEDED' }}
          - Cache Clear: {{ 'SUCCESS' if cache_clear_after_fix.rc == 0 else 'FAILED' }}
          
          🌐 Application Status After Fix:
          - HTTPS Response: {{ app_test_after_fortify_fix.status | default('Failed') }}
          - HTTP Response: {{ http_test_after_fortify_fix.status | default('Failed') }}
          
          📋 Latest Logs:
          {{ latest_logs_fortify.stdout }}
          
          🎯 Status:
          {% if app_test_after_fortify_fix.status == 200 or http_test_after_fortify_fix.status == 200 %}
          ✅ SUCCESS! Fortify error fixed, application working
          {% elif app_test_after_fortify_fix.status == 302 or http_test_after_fortify_fix.status == 302 %}
          ✅ REDIRECT! Application working (redirecting properly)
          {% else %}
          ⚠️  Still having issues. Check the logs above.
          {% endif %}
          
          🔗 Access URLs:
          - HTTPS: https://{{ ansible_host }}:8643
          - HTTP: http://{{ ansible_host }}:8601
