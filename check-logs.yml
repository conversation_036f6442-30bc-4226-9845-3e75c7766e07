---
- name: Check Logs and Troubleshoot Deployment Issues
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Check <PERSON>vel application logs (last 50 lines)
      shell: |
        tail -50 {{ app_root }}/storage/logs/laravel.log 2>/dev/null || echo "No Laravel log file found"
      register: laravel_logs
      ignore_errors: yes

    - name: Display Laravel logs
      debug:
        msg: |
          🔍 LARAVEL APPLICATION LOGS (Last 50 lines):
          ================================================
          {{ laravel_logs.stdout }}

    - name: Check Nginx error logs
      shell: |
        tail -30 /var/log/nginx/worksuite-error.log 2>/dev/null || echo "No Nginx error log found"
      register: nginx_error_logs
      ignore_errors: yes

    - name: Display Nginx error logs
      debug:
        msg: |
          🌐 NGINX ERROR LOGS (Last 30 lines):
          ====================================
          {{ nginx_error_logs.stdout }}

    - name: Check Nginx access logs
      shell: |
        tail -20 /var/log/nginx/worksuite-access.log 2>/dev/null || echo "No Nginx access log found"
      register: nginx_access_logs
      ignore_errors: yes

    - name: Display Nginx access logs
      debug:
        msg: |
          📊 NGINX ACCESS LOGS (Last 20 lines):
          =====================================
          {{ nginx_access_logs.stdout }}

    - name: Check PHP-FPM error logs
      shell: |
        tail -30 /var/log/php8.2-fpm.log 2>/dev/null || echo "No PHP-FPM log found"
      register: php_fpm_logs
      ignore_errors: yes

    - name: Display PHP-FPM logs
      debug:
        msg: |
          🐘 PHP-FPM LOGS (Last 30 lines):
          ================================
          {{ php_fpm_logs.stdout }}

    - name: Check system journal for services
      shell: |
        journalctl -u nginx -u php8.2-fpm -u mariadb -u redis-server --since "10 minutes ago" --no-pager -n 50
      register: system_journal
      ignore_errors: yes

    - name: Display system journal
      debug:
        msg: |
          📋 SYSTEM JOURNAL (Last 10 minutes):
          ===================================
          {{ system_journal.stdout }}

    - name: Check application environment file
      shell: |
        head -20 {{ app_root }}/.env | grep -E "(APP_|DB_|REDIS_)" || echo "Environment file check failed"
      register: env_check
      ignore_errors: yes

    - name: Display environment configuration
      debug:
        msg: |
          ⚙️ ENVIRONMENT CONFIGURATION:
          =============================
          {{ env_check.stdout }}

    - name: Test database connection directly
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            \$pdo = DB::connection()->getPdo();
            echo 'Database connection: SUCCESS\n';
            echo 'Database name: ' . DB::connection()->getDatabaseName() . '\n';
            \$tables = DB::select('SHOW TABLES');
            echo 'Number of tables: ' . count(\$tables) . '\n';
        } catch (Exception \$e) {
            echo 'Database connection: FAILED - ' . \$e->getMessage() . '\n';
        }
        "
      register: db_test
      ignore_errors: yes

    - name: Display database test results
      debug:
        msg: |
          🗄️ DATABASE CONNECTION TEST:
          ============================
          {{ db_test.stdout }}
          
          Errors (if any):
          {{ db_test.stderr }}

    - name: Check Laravel configuration cache
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan config:show | head -20
      register: config_show
      ignore_errors: yes

    - name: Display Laravel configuration
      debug:
        msg: |
          🔧 LARAVEL CONFIGURATION:
          =========================
          {{ config_show.stdout }}

    - name: Test application routes
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan route:list | head -10
      register: routes_test
      ignore_errors: yes

    - name: Display routes test
      debug:
        msg: |
          🛣️ APPLICATION ROUTES (First 10):
          =================================
          {{ routes_test.stdout }}

    - name: Check file permissions
      shell: |
        ls -la {{ app_root }}/storage/
        echo "---"
        ls -la {{ app_root }}/bootstrap/cache/
      register: permissions_check
      ignore_errors: yes

    - name: Display file permissions
      debug:
        msg: |
          📁 FILE PERMISSIONS:
          ===================
          {{ permissions_check.stdout }}

    - name: Check services status
      shell: |
        systemctl status nginx php8.2-fpm mariadb redis-server --no-pager -l
      register: services_status
      ignore_errors: yes

    - name: Display services status
      debug:
        msg: |
          🔄 SERVICES STATUS:
          ==================
          {{ services_status.stdout }}

    - name: Test HTTP response with curl
      shell: |
        curl -I -k https://localhost:8643 2>/dev/null || echo "HTTPS test failed"
        echo "---"
        curl -I http://localhost:8601 2>/dev/null || echo "HTTP test failed"
      register: curl_test
      ignore_errors: yes

    - name: Display HTTP test results
      debug:
        msg: |
          🌐 HTTP RESPONSE TEST:
          =====================
          {{ curl_test.stdout }}

    - name: Check disk space and memory
      shell: |
        df -h /var/www/
        echo "---"
        free -h
        echo "---"
        ps aux | grep -E "(nginx|php|mysql|redis)" | head -10
      register: system_resources
      ignore_errors: yes

    - name: Display system resources
      debug:
        msg: |
          💾 SYSTEM RESOURCES:
          ===================
          {{ system_resources.stdout }}

    - name: Generate comprehensive diagnostic report
      debug:
        msg: |
          🔍 COMPREHENSIVE DIAGNOSTIC REPORT
          ==================================
          
          📊 SUMMARY:
          - Laravel Logs: {{ 'Available' if laravel_logs.rc == 0 else 'Not Found' }}
          - Nginx Logs: {{ 'Available' if nginx_error_logs.rc == 0 else 'Not Found' }}
          - PHP-FPM Logs: {{ 'Available' if php_fpm_logs.rc == 0 else 'Not Found' }}
          - Database Connection: {{ 'Working' if 'SUCCESS' in db_test.stdout else 'Failed' }}
          - Routes: {{ 'Available' if routes_test.rc == 0 else 'Failed' }}
          
          🎯 RECOMMENDED ACTIONS:
          1. Check the Laravel logs above for specific error messages
          2. Verify database connection and migrations
          3. Ensure all file permissions are correct
          4. Check if all required services are running
          5. Clear application cache if needed
          
          🔧 QUICK FIXES TO TRY:
          - sudo -u worksuite php artisan cache:clear
          - sudo -u worksuite php artisan config:clear
          - sudo -u worksuite php artisan view:clear
          - sudo systemctl restart php8.2-fpm nginx
          
          📞 NEXT STEPS:
          Based on the logs above, identify the specific error and apply targeted fixes.
