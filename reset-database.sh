#!/bin/bash

# Reset Database and Run Fresh Migration Script
# This script will completely reset the WorkSuite SAAS database and run fresh migrations

set -e

echo "🔄 Starting Database Reset Process"
echo "=================================="

# Check if inventory file exists
if [ ! -f "inventory/hosts.yml" ]; then
    echo "❌ Error: Ansible inventory file not found!"
    echo "Please ensure inventory/hosts.yml exists."
    exit 1
fi

echo "✅ Ansible inventory found"

# Check connectivity
echo "📡 Testing connection to server..."
if ansible all -i inventory/hosts.yml -m ping > /dev/null 2>&1; then
    echo "✅ Server connection successful"
else
    echo "❌ Error: Cannot connect to server!"
    echo "Please check your SSH connection and inventory configuration."
    exit 1
fi

# Confirm action
echo ""
echo "⚠️  WARNING: This will completely delete the current database!"
echo "   - All existing data will be lost"
echo "   - Fresh migration will be run"
echo "   - Database will be recreated from scratch"
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "❌ Operation cancelled by user"
    exit 0
fi

echo ""
echo "🚀 Starting database reset..."
echo "This may take a few minutes..."

# Run the reset playbook
if ansible-playbook -i inventory/hosts.yml reset-database.yml -v; then
    echo ""
    echo "🎉 Database Reset Successful!"
    echo "============================"
    echo ""
    echo "✅ Database has been completely reset"
    echo "✅ Fresh migrations have been run"
    echo "✅ Services have been restarted"
    echo ""
    echo "🌐 Access Information:"
    echo "   HTTP:  http://ec2-52-77-233-254.ap-southeast-1.compute.amazonaws.com:8601"
    echo "   HTTPS: https://ec2-52-77-233-254.ap-southeast-1.compute.amazonaws.com:8643"
    echo "   Domain: https://erp.iti.id.vn"
    echo ""
    echo "📝 Next Steps:"
    echo "   1. Visit one of the URLs above"
    echo "   2. Complete the WorkSuite setup wizard"
    echo "   3. Create your admin account"
    echo "   4. Configure your application settings"
    echo ""
else
    echo ""
    echo "❌ Database Reset Failed!"
    echo "======================="
    echo ""
    echo "Please check the error messages above and try again."
    echo "You can also check the logs manually:"
    echo "   - Application logs: /var/www/worksuite/storage/logs/laravel.log"
    echo "   - Nginx logs: /var/log/nginx/worksuite-error.log"
    echo ""
    exit 1
fi
