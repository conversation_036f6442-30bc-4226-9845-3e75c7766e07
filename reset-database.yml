---
- name: Reset Database and Run Fresh Migration
  hosts: worksuite_servers
  become: yes
  vars:
    # Use hardcoded values to avoid recursive template issues
    mysql_root_password: "WS_RootPass_2024_Secure!"
    mysql_database: "worksuite_saas"
    mysql_user: "worksuite_user"
    mysql_password: "WS_UserPass_2024_Secure!"
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Stop Laravel queue service before database reset
      systemd:
        name: laravel-queue
        state: stopped
      ignore_errors: yes

    - name: Clear all Laravel caches before reset
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear || true
        sudo -u {{ app_user }} php artisan config:clear || true
        sudo -u {{ app_user }} php artisan route:clear || true
        sudo -u {{ app_user }} php artisan view:clear || true
      ignore_errors: yes

    - name: Drop existing database
      mysql_db:
        name: "{{ mysql_database }}"
        state: absent
        login_user: root
        login_password: "{{ mysql_root_password }}"
      register: db_drop_result

    - name: Display database drop result
      debug:
        msg: "Database {{ mysql_database }} dropped: {{ db_drop_result.changed }}"

    - name: Create fresh database
      mysql_db:
        name: "{{ mysql_database }}"
        state: present
        encoding: utf8mb4
        collation: utf8mb4_unicode_ci
        login_user: root
        login_password: "{{ mysql_root_password }}"
      register: db_create_result

    - name: Display database creation result
      debug:
        msg: "Database {{ mysql_database }} created: {{ db_create_result.changed }}"

    - name: Grant privileges to application user
      mysql_user:
        name: "{{ mysql_user }}"
        password: "{{ mysql_password }}"
        priv: "{{ mysql_database }}.*:ALL"
        host: localhost
        state: present
        login_user: root
        login_password: "{{ mysql_root_password }}"
      register: user_grant_result

    - name: Display user grant result
      debug:
        msg: "User privileges granted: {{ user_grant_result.changed }}"

    - name: Test database connection
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connection successful';"
      register: db_test_result
      ignore_errors: yes

    - name: Display database connection test
      debug:
        msg: "Database connection test: {{ db_test_result.stdout if db_test_result.rc == 0 else 'Failed: ' + db_test_result.stderr }}"

    - name: Run fresh migration
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan migrate:fresh --force
      register: migration_result
      ignore_errors: yes

    - name: Display migration result
      debug:
        msg: |
          Migration Result:
          {{ migration_result.stdout }}
          
          Errors (if any):
          {{ migration_result.stderr }}

    - name: Check migration status
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan migrate:status
      register: migration_status
      ignore_errors: yes

    - name: Display migration status
      debug:
        msg: |
          Migration Status:
          {{ migration_status.stdout }}

    - name: Run database seeder
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan db:seed --force
      register: seeder_result
      ignore_errors: yes

    - name: Display seeder result
      debug:
        msg: |
          Seeder Result:
          {{ seeder_result.stdout }}
          
          Errors (if any):
          {{ seeder_result.stderr }}

    - name: Create storage symbolic link
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan storage:link
      register: storage_link_result
      ignore_errors: yes

    - name: Set proper file permissions
      shell: |
        chown -R {{ app_user }}:www-data {{ app_root }}/storage
        chown -R {{ app_user }}:www-data {{ app_root }}/bootstrap/cache
        chmod -R 775 {{ app_root }}/storage
        chmod -R 775 {{ app_root }}/bootstrap/cache
      register: permissions_result

    - name: Clear and optimize application after reset
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear
        sudo -u {{ app_user }} php artisan config:cache
        sudo -u {{ app_user }} php artisan route:cache
        sudo -u {{ app_user }} php artisan view:cache
      register: optimize_result
      ignore_errors: yes

    - name: Start Laravel queue service
      systemd:
        name: laravel-queue
        state: started
        enabled: yes

    - name: Restart PHP-FPM service
      systemd:
        name: php8.2-fpm
        state: restarted

    - name: Restart Nginx service
      systemd:
        name: nginx
        state: restarted

    - name: Wait for services to be ready
      wait_for:
        port: "{{ item }}"
        host: localhost
        timeout: 30
      loop:
        - 8601
        - 8643
      ignore_errors: yes

    - name: Test application response after reset
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 10
      register: app_test_result
      ignore_errors: yes

    - name: Check application logs for any errors
      shell: |
        tail -20 {{ app_root }}/storage/logs/laravel.log 2>/dev/null || echo "No log file found"
      register: app_logs

    - name: Display final reset status
      debug:
        msg: |
          🔄 DATABASE RESET COMPLETE
          ===========================
          
          📊 Database Status: {{ 'SUCCESS' if db_create_result.changed else 'FAILED' }}
          
          📋 Migration Status: {{ 'SUCCESS' if migration_result.rc == 0 else 'FAILED' }}
          
          🌱 Seeding Status: {{ 'SUCCESS' if seeder_result.rc == 0 else 'FAILED' }}
          
          🔗 Storage Link: {{ 'SUCCESS' if storage_link_result.rc == 0 else 'FAILED' }}
          
          🌐 Application Status: {{ app_test_result.status | default('Unknown') }}
          
          🌐 Access URLs:
          - HTTP: http://{{ ansible_host }}:8601
          - HTTPS: https://{{ ansible_host }}:8643
          - Public: https://erp.iti.id.vn
          
          📋 Recent Logs:
          {{ app_logs.stdout }}
          
          📝 Next Steps:
          1. Try accessing the application URLs above
          2. Complete the WorkSuite setup wizard if needed
          3. Create admin user and configure settings
          
          🔑 Default Credentials (if applicable):
          - Check the application setup wizard
          - Common defaults: <EMAIL> / password123
