---
- name: Fix Route Login Error in FortifyServiceProvider
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Check current FortifyServiceProvider content around the fix
      shell: |
        grep -A 5 -B 5 "redirect" {{ app_root }}/app/Providers/FortifyServiceProvider.php || echo "No redirect found"
      register: current_fortify_content

    - name: Display current FortifyServiceProvider content
      debug:
        msg: |
          Current FortifyServiceProvider content:
          {{ current_fortify_content.stdout }}

    - name: Fix FortifyServiceProvider to use proper redirect
      replace:
        path: "{{ app_root }}/app/Providers/FortifyServiceProvider.php"
        regexp: 'redirect\(\)->route\("login"\)'
        replace: 'redirect("/")'
      register: fix_redirect_result

    - name: Alternative fix - replace any redirect()->route("login") calls
      replace:
        path: "{{ app_root }}/app/Providers/FortifyServiceProvider.php"
        regexp: 'return redirect\(\)->route\("login"\);'
        replace: 'return redirect("/");'
      register: fix_redirect_alt_result

    - name: Check if we still have route login issues
      shell: |
        grep -n "route.*login" {{ app_root }}/app/Providers/FortifyServiceProvider.php || echo "No route login found"
      register: check_route_login

    - name: Display route login check
      debug:
        msg: |
          Route login check:
          {{ check_route_login.stdout }}

    - name: Remove any problematic lines with route login
      lineinfile:
        path: "{{ app_root }}/app/Providers/FortifyServiceProvider.php"
        regexp: '.*redirect\(\)->route\("login"\).*'
        state: absent
      register: remove_route_login

    - name: Check routes file to see available routes
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan route:list | grep -i login | head -5 || echo "No login routes found"
      register: available_routes
      ignore_errors: yes

    - name: Display available routes
      debug:
        msg: |
          Available login routes:
          {{ available_routes.stdout }}

    - name: Try to clear caches with simpler approach
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan cache:clear || echo "Cache clear failed"
        sudo -u {{ app_user }} php artisan config:clear || echo "Config clear failed"
      register: simple_cache_clear
      ignore_errors: yes

    - name: Display cache clear results
      debug:
        msg: |
          Cache clear results:
          {{ simple_cache_clear.stdout }}

    - name: Check if FortifyServiceProvider has syntax errors
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php -l app/Providers/FortifyServiceProvider.php
      register: syntax_check
      ignore_errors: yes

    - name: Display syntax check results
      debug:
        msg: |
          Syntax check results:
          {{ syntax_check.stdout }}

    - name: Restart PHP-FPM service
      systemd:
        name: php8.2-fpm
        state: restarted

    - name: Test application response after route fix
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 10
      register: app_test_after_route_fix
      ignore_errors: yes

    - name: Test HTTP response after route fix
      uri:
        url: "http://localhost:8601"
        method: GET
        timeout: 10
      register: http_test_after_route_fix
      ignore_errors: yes

    - name: Check latest Laravel logs after route fix
      shell: |
        tail -20 {{ app_root }}/storage/logs/laravel.log 2>/dev/null || echo "No new log entries"
      register: latest_logs_route

    - name: Display route fix results
      debug:
        msg: |
          🔧 ROUTE LOGIN FIX COMPLETE
          ===========================
          
          📊 Fix Results:
          - Redirect Fix: {{ 'SUCCESS' if fix_redirect_result.changed or fix_redirect_alt_result.changed else 'NO CHANGES' }}
          - Route Login Removal: {{ 'SUCCESS' if remove_route_login.changed else 'NO CHANGES' }}
          - Syntax Check: {{ 'PASSED' if 'No syntax errors' in syntax_check.stdout else 'ISSUES' }}
          
          🌐 Application Status After Fix:
          - HTTPS Response: {{ app_test_after_route_fix.status | default('Failed') }}
          - HTTP Response: {{ http_test_after_route_fix.status | default('Failed') }}
          
          📋 Latest Logs:
          {{ latest_logs_route.stdout }}
          
          🎯 Status:
          {% if app_test_after_route_fix.status == 200 or http_test_after_route_fix.status == 200 %}
          ✅ SUCCESS! Route error fixed, application working
          {% elif app_test_after_route_fix.status == 302 or http_test_after_route_fix.status == 302 %}
          ✅ REDIRECT! Application working (redirecting properly)
          {% else %}
          ⚠️  Still having issues. Check the logs above.
          {% endif %}
          
          🔗 Access URLs:
          - HTTPS: https://{{ ansible_host }}:8643
          - HTTP: http://{{ ansible_host }}:8601
