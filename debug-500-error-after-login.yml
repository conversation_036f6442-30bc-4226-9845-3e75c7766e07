---
- name: Debug 500 Error After Login
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Check latest Laravel logs for 500 errors
      shell: |
        cd {{ app_root }}
        echo "=== Latest Laravel Error Logs ==="
        tail -50 storage/logs/laravel.log | grep -A 10 -B 5 "ERROR\|CRITICAL\|Exception" || echo "No recent errors found"
      register: laravel_logs

    - name: Display Laravel logs
      debug:
        msg: |
          Laravel Error Logs:
          {{ laravel_logs.stdout }}

    - name: Check Nginx error logs
      shell: |
        echo "=== Latest Nginx Error Logs ==="
        tail -30 /var/log/nginx/worksuite-error.log 2>/dev/null || echo "No Nginx errors"
        echo ""
        echo "=== Latest Nginx HTTPS Error Logs ==="
        tail -30 /var/log/nginx/worksuite-https-error.log 2>/dev/null || echo "No HTTPS errors"
      register: nginx_logs

    - name: Display Nginx logs
      debug:
        msg: |
          Nginx Error Logs:
          {{ nginx_logs.stdout }}

    - name: Check PHP-FPM error logs
      shell: |
        echo "=== Latest PHP-FPM Error Logs ==="
        tail -30 /var/log/php8.2-fpm-worksuite.log 2>/dev/null || echo "No PHP-FPM errors"
        echo ""
        echo "=== System PHP-FPM Logs ==="
        journalctl -u php8.2-fpm --since "10 minutes ago" --no-pager | tail -20 || echo "No system PHP-FPM logs"
      register: php_logs

    - name: Display PHP logs
      debug:
        msg: |
          PHP-FPM Error Logs:
          {{ php_logs.stdout }}

    - name: Test basic Laravel functionality
      shell: |
        cd {{ app_root }}
        echo "=== Testing Laravel Basic Commands ==="
        sudo -u {{ app_user }} php artisan --version || echo "Artisan version failed"
        echo ""
        echo "=== Testing Database Connection ==="
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            \$result = DB::connection()->getPdo();
            echo 'Database connection: SUCCESS' . PHP_EOL;
        } catch (Exception \$e) {
            echo 'Database connection: FAILED - ' . \$e->getMessage() . PHP_EOL;
        }
        " || echo "Database test failed"
      register: laravel_test

    - name: Display Laravel test results
      debug:
        msg: |
          Laravel Test Results:
          {{ laravel_test.stdout }}

    - name: Check session and cache configuration
      shell: |
        cd {{ app_root }}
        echo "=== Session Configuration ==="
        sudo -u {{ app_user }} php artisan config:show session || echo "Session config failed"
        echo ""
        echo "=== Cache Configuration ==="
        sudo -u {{ app_user }} php artisan config:show cache || echo "Cache config failed"
      register: session_cache_config

    - name: Display session and cache config
      debug:
        msg: |
          Session and Cache Configuration:
          {{ session_cache_config.stdout }}

    - name: Check Redis connection
      shell: |
        echo "=== Testing Redis Connection ==="
        redis-cli ping || echo "Redis ping failed"
        echo ""
        echo "=== Redis Info ==="
        redis-cli info server | head -10 || echo "Redis info failed"
      register: redis_test

    - name: Display Redis test
      debug:
        msg: |
          Redis Test Results:
          {{ redis_test.stdout }}

    - name: Check file permissions on critical directories
      shell: |
        cd {{ app_root }}
        echo "=== File Permissions Check ==="
        ls -la storage/ | head -10
        echo ""
        echo "=== Bootstrap Cache Permissions ==="
        ls -la bootstrap/cache/ | head -5
        echo ""
        echo "=== Storage Logs Permissions ==="
        ls -la storage/logs/ | head -5
      register: permissions_check

    - name: Display permissions check
      debug:
        msg: |
          File Permissions:
          {{ permissions_check.stdout }}

    - name: Clear all caches and regenerate
      shell: |
        cd {{ app_root }}
        echo "=== Clearing All Caches ==="
        sudo -u {{ app_user }} php artisan cache:clear || echo "Cache clear failed"
        sudo -u {{ app_user }} php artisan config:clear || echo "Config clear failed"
        sudo -u {{ app_user }} php artisan route:clear || echo "Route clear failed"
        sudo -u {{ app_user }} php artisan view:clear || echo "View clear failed"
        sudo -u {{ app_user }} php artisan session:flush || echo "Session flush failed"
        echo ""
        echo "=== Regenerating Caches ==="
        sudo -u {{ app_user }} php artisan config:cache || echo "Config cache failed"
        sudo -u {{ app_user }} php artisan route:cache || echo "Route cache failed"
        sudo -u {{ app_user }} php artisan view:cache || echo "View cache failed"
      register: cache_operations

    - name: Display cache operations
      debug:
        msg: |
          Cache Operations:
          {{ cache_operations.stdout }}

    - name: Check Laravel environment and debug settings
      shell: |
        cd {{ app_root }}
        echo "=== Environment Settings ==="
        grep -E "(APP_ENV|APP_DEBUG|LOG_LEVEL)" .env || echo "No env settings found"
        echo ""
        echo "=== Laravel Config ==="
        sudo -u {{ app_user }} php artisan config:show app.env || echo "App env failed"
        sudo -u {{ app_user }} php artisan config:show app.debug || echo "App debug failed"
      register: env_debug_check

    - name: Display environment and debug settings
      debug:
        msg: |
          Environment and Debug Settings:
          {{ env_debug_check.stdout }}

    - name: Enable debug mode temporarily
      lineinfile:
        path: "{{ app_root }}/.env"
        regexp: '^APP_DEBUG='
        line: 'APP_DEBUG=true'
        state: present
      register: enable_debug

    - name: Clear config cache after debug enable
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan config:clear
        sudo -u {{ app_user }} php artisan config:cache
      when: enable_debug.changed

    - name: Test application response after fixes
      shell: |
        echo "=== Testing Application Response ==="
        curl -I -k https://localhost:8643 2>/dev/null | head -5 || echo "HTTPS test failed"
        echo ""
        curl -I http://localhost:8601 2>/dev/null | head -5 || echo "HTTP test failed"
      register: app_response_test

    - name: Display application response test
      debug:
        msg: |
          Application Response Test:
          {{ app_response_test.stdout }}

    - name: Check for specific WorkSuite SAAS errors
      shell: |
        cd {{ app_root }}
        echo "=== Checking for WorkSuite Specific Issues ==="
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            \$company = \App\Models\Company::first();
            if (\$company) {
                echo 'Company found: ' . \$company->company_name . PHP_EOL;
            } else {
                echo 'No company found' . PHP_EOL;
            }
            
            \$user = \App\Models\User::first();
            if (\$user) {
                echo 'User found: ' . \$user->name . PHP_EOL;
            } else {
                echo 'No user found' . PHP_EOL;
            }
        } catch (Exception \$e) {
            echo 'WorkSuite check error: ' . \$e->getMessage() . PHP_EOL;
        }
        " || echo "WorkSuite check failed"
      register: worksuite_check

    - name: Display comprehensive debug results
      debug:
        msg: |
          🔧 500 ERROR DEBUG COMPLETE
          ===========================
          
          📊 Laravel Logs:
          {{ laravel_logs.stdout | default('No Laravel logs') }}
          
          🌐 Nginx Logs:
          {{ nginx_logs.stdout | default('No Nginx logs') }}
          
          🐘 PHP-FPM Logs:
          {{ php_logs.stdout | default('No PHP logs') }}
          
          ⚙️ Laravel Test:
          {{ laravel_test.stdout | default('Laravel test failed') }}
          
          💾 Redis Test:
          {{ redis_test.stdout | default('Redis test failed') }}
          
          📁 Permissions:
          {{ permissions_check.stdout | default('Permission check failed') }}
          
          🗂️ Cache Operations:
          {{ cache_operations.stdout | default('Cache operations failed') }}
          
          🔍 Environment:
          {{ env_debug_check.stdout | default('Environment check failed') }}
          
          🌐 App Response:
          {{ app_response_test.stdout | default('App response test failed') }}
          
          🏢 WorkSuite Check:
          {{ worksuite_check.stdout | default('WorkSuite check failed') }}
          
          🎯 Next Steps:
          1. Check the Laravel logs above for specific error messages
          2. Debug mode is now enabled for detailed error messages
          3. Try accessing the application again
          4. If still failing, check the specific error in browser
          
          🔗 Test URLs:
          - HTTPS: https://{{ ansible_host }}:8643
          - HTTP: http://{{ ansible_host }}:8601
          
          📝 Debug Mode: ENABLED
          Now you should see detailed error messages in the browser.
