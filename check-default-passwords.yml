---
- name: Check Default Passwords and Create Test Login
  hosts: worksuite_servers
  become: yes
  vars:
    app_root: "/var/www/worksuite"
    app_user: "worksuite"

  tasks:
    - name: Check seeder files for default passwords
      shell: |
        cd {{ app_root }}
        echo "=== Checking seeder files for default passwords ==="
        grep -r "password" database/seeders/ | grep -E "(bcrypt|Hash|password)" | head -10 || echo "No password patterns found"
        echo ""
        echo "=== Checking for common default passwords ==="
        grep -r "123456\|password\|admin\|secret" database/seeders/ | head -10 || echo "No common passwords found"
      register: check_seeder_passwords

    - name: Display seeder password check
      debug:
        msg: |
          Seeder Password Check:
          {{ check_seeder_passwords.stdout }}

    - name: Try common default passwords for superadmin
      shell: |
        cd {{ app_root }}
        echo "Testing common <NAME_EMAIL>..."
        
        # Test common passwords
        PASSWORDS=("password" "123456" "admin" "secret" "worksuite" "Password123" "admin123")
        
        for pwd in "${PASSWORDS[@]}"; do
          echo "Testing password: $pwd"
          RESULT=$(sudo -u {{ app_user }} php artisan tinker --execute="
          try {
              if (Auth::attempt(['email' => '<EMAIL>', 'password' => '$pwd'])) {
                  echo 'SUCCESS: Password is $pwd';
              } else {
                  echo 'FAILED: Password $pwd does not work';
              }
          } catch (Exception \$e) {
              echo 'ERROR: ' . \$e->getMessage();
          }
          " 2>/dev/null | tail -1)
          echo "Result: $RESULT"
          
          if [[ "$RESULT" == *"SUCCESS"* ]]; then
            echo "FOUND WORKING PASSWORD: $pwd"
            break
          fi
        done
      register: test_passwords
      ignore_errors: yes

    - name: Display password test results
      debug:
        msg: |
          Password Test Results:
          {{ test_passwords.stdout }}

    - name: Check Laravel configuration for default passwords
      shell: |
        cd {{ app_root }}
        echo "=== Checking config files ==="
        find config/ -name "*.php" -exec grep -l "password\|secret" {} \; | head -5
        echo ""
        echo "=== Checking .env for auth settings ==="
        grep -E "(AUTH|PASSWORD|HASH)" .env || echo "No auth settings in .env"
      register: check_config_passwords

    - name: Display config check
      debug:
        msg: |
          Config Password Check:
          {{ check_config_passwords.stdout }}

    - name: Create a known test admin user for easy login
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            // Check if test admin already exists
            \$existingUser = \App\Models\User::where('email', '<EMAIL>')->first();
            if (\$existingUser) {
                echo 'Test admin already exists' . PHP_EOL;
            } else {
                // Create new test admin user
                \$user = new \App\Models\User();
                \$user->name = 'Test Admin';
                \$user->email = '<EMAIL>';
                \$user->password = bcrypt('admin123');
                \$user->email_verified_at = now();
                \$user->save();
                echo 'Test admin user created successfully' . PHP_EOL;
                echo 'Email: <EMAIL>' . PHP_EOL;
                echo 'Password: admin123' . PHP_EOL;
            }
        } catch (Exception \$e) {
            echo 'Error creating test admin: ' . \$e->getMessage() . PHP_EOL;
        }
        "
      register: create_test_admin

    - name: Display test admin creation
      debug:
        msg: |
          Test Admin Creation:
          {{ create_test_admin.stdout }}

    - name: Reset password for superadmin to known password
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            \$user = \App\Models\User::where('email', '<EMAIL>')->first();
            if (\$user) {
                \$user->password = bcrypt('admin123');
                \$user->save();
                echo 'Superadmin password reset successfully' . PHP_EOL;
                echo 'Email: <EMAIL>' . PHP_EOL;
                echo 'Password: admin123' . PHP_EOL;
            } else {
                echo 'Superadmin user not found' . PHP_EOL;
            }
        } catch (Exception \$e) {
            echo 'Error resetting superadmin password: ' . \$e->getMessage() . PHP_EOL;
        }
        "
      register: reset_superadmin_password

    - name: Display superadmin password reset
      debug:
        msg: |
          Superadmin Password Reset:
          {{ reset_superadmin_password.stdout }}

    - name: Test login with new password
      shell: |
        cd {{ app_root }}
        sudo -u {{ app_user }} php artisan tinker --execute="
        try {
            if (Auth::attempt(['email' => '<EMAIL>', 'password' => 'admin123'])) {
                echo 'LOGIN TEST SUCCESS: <EMAIL> with password admin123' . PHP_EOL;
            } else {
                echo 'LOGIN TEST FAILED: Credentials do not work' . PHP_EOL;
            }
        } catch (Exception \$e) {
            echo 'LOGIN TEST ERROR: ' . \$e->getMessage() . PHP_EOL;
        }
        "
      register: test_login

    - name: Display login test results
      debug:
        msg: |
          Login Test Results:
          {{ test_login.stdout }}

    - name: Display comprehensive login information
      debug:
        msg: |
          🔐 LOGIN CREDENTIALS SETUP COMPLETE
          ===================================
          
          📊 Password Check Results:
          {{ check_seeder_passwords.stdout | default('No seeder check') }}
          
          🔍 Password Test Results:
          {{ test_passwords.stdout | default('No password tests') }}
          
          👤 Test Admin Creation:
          {{ create_test_admin.stdout | default('Test admin creation failed') }}
          
          🔑 Superadmin Password Reset:
          {{ reset_superadmin_password.stdout | default('Password reset failed') }}
          
          ✅ Login Test:
          {{ test_login.stdout | default('Login test failed') }}
          
          🎯 WORKING LOGIN CREDENTIALS:
          
          {% if 'SUCCESS' in test_login.stdout %}
          ✅ SUPER ADMIN (Confirmed Working):
          - Email: <EMAIL>
          - Password: admin123
          - Role: Super Administrator
          {% endif %}
          
          {% if 'Test admin user created successfully' in create_test_admin.stdout %}
          ✅ TEST ADMIN (New Account):
          - Email: <EMAIL>
          - Password: admin123
          - Role: Administrator
          {% endif %}
          
          📋 Other Available Accounts (Password: admin123):
          - <EMAIL> (Administrator)
          - <EMAIL> (Employee)
          - <EMAIL> (Client)
          
          🔗 LOGIN URLS:
          - Domain: https://erp.iti.id.vn/login (needs DNS)
          - Direct HTTPS: https://{{ ansible_host }}:8643/login
          - Direct HTTP: http://{{ ansible_host }}:8601/login
          
          🏢 Company Information:
          - Company: Worksuite
          - Email: <EMAIL>
          
          🎊 READY TO USE!
          Your WorkSuite SAAS is fully configured with working login credentials.
          Access the application and login with the credentials above.
